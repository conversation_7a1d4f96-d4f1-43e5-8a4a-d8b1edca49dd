<template>
  <view class="login-container">
    <!-- 内容区域 -->
    <view class="content">
      <!-- 品牌logo -->
      <view class="logo">
        <view class="logo-container">
          <up-icon name="account" size="80" color="#1e88e5" />
        </view>
      </view>
      
      <!-- 标题区域 -->
      <view class="title-area">
        <text class="main-title">疾控考试系统</text>
        <text class="sub-title">医护任职资格考试平台</text>
      </view>
      
      <!-- 微信登录按钮 -->
      <view class="login-btn-container">
        <up-button
          type="primary"
          :disabled="!agreedToTerms || isLoading"
          :loading="isLoading"
          loadingText="登录中..."
          :customStyle="loginButtonStyle"
          shape="round"
          size="large"
          :throttleTime="1000"
          @click="handleWxLogin"
        >
          <view class="btn-content">
            <up-icon name="weixin-fill" size="18" color="#1e88e5" />
            <text class="btn-text">微信授权登录</text>
          </view>
        </up-button>
      </view>
      
      <!-- 协议确认 -->
      <view class="agreement">
        <view class="checkbox-wrapper">
          <up-checkbox
            v-model="agreedToTerms"
            activeColor="#ffffff"
            inactiveColor="rgba(255,255,255,0.6)"
            iconColor="#1e88e5"
            size="20"
            iconSize="14"
            shape="square"
            :disabled="isLoading"
          >
            我已阅读并同意
          </up-checkbox>
        </view>
        <view class="agreement-links">
          <text class="link" @click="showUserAgreement">《用户服务协议》</text>
          <text class="agreement-text">和</text>
          <text class="link" @click="showPrivacyPolicy">《隐私政策》</text>
        </view>
      </view>
    </view>

    <!-- 用户协议模态框 -->
    <up-modal
      v-model="showUserAgreementModal"
      title="用户服务协议"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="showUserAgreementModal = false"
    >
      <view class="modal-content">
        <up-text
          :text="userAgreementContent"
          :size="modalTextSize"
          color="#212121"
          :lineHeight="modalTextLineHeight"
        />
      </view>
    </up-modal>

    <!-- 隐私政策模态框 -->
    <up-modal
      v-model="showPrivacyPolicyModal"
      title="隐私政策"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="showPrivacyPolicyModal = false"
    >
      <view class="modal-content">
        <up-text
          :text="privacyPolicyContent"
          :size="modalTextSize"
          color="#212121"
          :lineHeight="modalTextLineHeight"
        />
      </view>
    </up-modal>

    <!-- Toast 消息提示 -->
    <up-toast ref="toastRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/src/stores/modules/user'
import { wxLogin } from '@/src/api/modules/user'
import type { LoginParams, UserInfo } from '@/src/types/api'

// ==================== Interfaces ====================
interface ToastInstance {
  show: (options: {
    title: string
    type?: 'success' | 'error' | 'warning' | 'info'
    duration?: number
  }) => void
}

// ==================== Store ====================
const userStore = useUserStore()
const { profile } = storeToRefs(userStore)
const { setProfile } = userStore

// ==================== 响应式数据 ====================
/** 是否同意用户协议 */
const agreedToTerms = ref<boolean>(false)
/** 登录加载状态 */
const isLoading = ref<boolean>(false)
/** 显示用户协议模态框 */
const showUserAgreementModal = ref<boolean>(false)
/** 显示隐私政策模态框 */
const showPrivacyPolicyModal = ref<boolean>(false)

// ==================== Toast 引用 ====================
const toastRef = ref<ToastInstance | null>(null)

// ==================== 设计系统 ====================
/** 字体尺寸 - 使用px单位，符合uview-plus规范 */
const modalTextSize = computed(() => 14)        // 模态框文字

/** 行高 */
const modalTextLineHeight = computed(() => 22)

/** 登录按钮样式 - Apple风格 */
const loginButtonStyle = computed(() => ({
  width: '80%',
  height: '92rpx',
  backgroundColor: agreedToTerms.value ? '#ffffff' : 'rgba(255,255,255,0.6)',
  color: '#1e88e5',
  border: agreedToTerms.value ? '2rpx solid #1e88e5' : '2rpx solid rgba(30,136,229,0.3)',
  borderRadius: '46rpx',
  boxShadow: agreedToTerms.value 
    ? '0 8rpx 24rpx rgba(30,136,229,0.15), 0 2rpx 8rpx rgba(30,136,229,0.1)' 
    : '0 4rpx 12rpx rgba(0,0,0,0.1)',
  transition: 'all 0.3s ease',
  opacity: agreedToTerms.value ? 1 : 0.7,
}))

/** 用户协议内容 */
const userAgreementContent = computed(() => `1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。`)

/** 隐私政策内容 */
const privacyPolicyContent = computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。`)

// ==================== 事件处理 ====================


/**
 * 显示用户服务协议
 */
function showUserAgreement(): void {
  showUserAgreementModal.value = true
}

/**
 * 显示隐私政策
 */
function showPrivacyPolicy(): void {
  showPrivacyPolicyModal.value = true
}

/**
 * 显示Toast消息
 * @param title 消息标题
 * @param type 消息类型
 */
function showToast(title: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
  if (toastRef.value) {
    toastRef.value.show({
      title,
      type,
      duration: type === 'success' ? 1500 : 2000,
    })
  }
}

/**
 * 微信授权登录
 */
async function handleWxLogin(): Promise<void> {
  // 检查协议同意状态
  if (!agreedToTerms.value) {
    showToast('请先同意用户协议', 'warning')
    return
  }

  isLoading.value = true

  try {
    // 调用微信登录获取code
    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject,
      })
    })

    // 构造登录参数
    const loginParams: LoginParams = {
      code: loginResult.code,
    }

    // 调用后端登录接口
    const userInfo: UserInfo = await wxLogin(loginParams)

    // 保存用户信息到Store
    setProfile(userInfo)

    // 登录成功提示
    showToast('登录成功', 'success')

    // 根据用户状态进行页面跳转
    setTimeout(() => {
      navigateByUserStatus(userInfo.status)
    }, 1500)

  } catch (error) {
    console.error('微信登录失败:', error)
    showToast('登录失败，请重试', 'error')
  } finally {
    isLoading.value = false
  }
}

/**
 * 根据用户状态进行页面跳转
 * @param status 用户状态
 */
function navigateByUserStatus(status: UserInfo['status']): void {
  switch (status) {
    case 'approved':
      // 已审核通过的正式用户，跳转到信息中心
      uni.reLaunch({ url: '/pages/info/info' })
      break
    case 'pending':
      // 待审核用户，跳转到个人中心查看审核状态
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'rejected':
      // 审核未通过用户，跳转到个人中心修改资料
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'incomplete':
    default:
      // 未提交资料的新用户，跳转到注册页面
      uni.navigateTo({ url: '/pages/register/register' })
      break
  }
}
</script>

<style scoped>

/* ==================== 页面全局设置 ==================== */
.login-container {
  /* Apple风格渐变+噪点，合并为多层background */
  background:
    linear-gradient(180deg,#60a5fa 0%,#1e88e5 100%),
    radial-gradient(circle at 20% 80%,rgba(255,255,255,.06) 0%,transparent 40%),
    radial-gradient(circle at 80% 20%,rgba(255,255,255,.06) 0%,transparent 40%);
  min-height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-left: 0;
  padding-right: 0;
  overflow: hidden;
}

/* ==================== 主容器 ==================== */
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  justify-content: center;
  gap: 160rpx;
  max-width: 600rpx;
  position: relative;
  z-index: 10;
}

/* ==================== Logo区域 ==================== */
.logo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50rpx;
  flex-shrink: 0;
}

.logo-container {
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 
    0 8rpx 32rpx rgba(255, 255, 255, 0.1),
    0 2rpx 8rpx rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* ==================== 标题区域 ==================== */
.title-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 移除margin-bottom: 200rpx; */
  flex-shrink: 0;
}

.main-title {
  font-size: 48rpx;
  color: #ffffff !important;
  font-weight: 600;
  margin-bottom: 20rpx;
  letter-spacing: .5rpx;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.sub-title {
  font-size: 32rpx;
  color: rgba(255,255,255,.85) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* ==================== 登录按钮区域 ==================== */
.login-btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 0;
  flex-shrink: 0;
}

.btn-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  color: #1e88e5 !important;
  font-size: 32rpx;
  font-weight: 500;
  margin-left: 12rpx;
}

/* ==================== 协议区域 ==================== */
.agreement {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 24rpx;
}

.agreement-label {
  font-size: 26rpx;
  color: #ffffff !important;
  margin-left: 16rpx;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.agreement-links {
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255,255,255,.9) !important;
  margin: 0 8rpx;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.link {
  font-size: 24rpx;
  color: #ffffff !important;
  text-decoration: underline;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* ==================== uview-plus组件样式强制覆盖 ==================== */
/* 强制设置所有图标颜色 */
:deep(.up-icon),
:deep(.u-icon) {
  color: #ffffff !important;
}

/* 强制设置按钮内文字颜色 */
:deep(.up-button),
:deep(.u-button) {
  color: #ffffff !important;
}

:deep(.up-button .up-button__text),
:deep(.u-button .u-button__text) {
  color: #ffffff !important;
}

/* 强制设置复选框文字颜色 - 确保可见 */
:deep(.up-checkbox__label),
:deep(.u-checkbox__label) {
  color: #ffffff !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* ==================== 复选框样式优化 ==================== */
:deep(.up-checkbox-group),
:deep(.u-checkbox-group) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.up-checkbox),
:deep(.u-checkbox) {
  display: flex;
  align-items: center;
}

:deep(.up-checkbox__icon-wrap),
:deep(.u-checkbox__icon-wrap) {
  margin-right: 0;
  border: 2rpx solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 6rpx !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1) !important;
  width: 40rpx !important;
  height: 40rpx !important;
}

/* 复选框选中状态 */
:deep(.up-checkbox--checked .up-checkbox__icon-wrap),
:deep(.u-checkbox--checked .u-checkbox__icon-wrap) {
  background-color: #ffffff !important;
  border-color: #ffffff !important;
}

/* 复选框图标 */
:deep(.up-checkbox__icon),
:deep(.u-checkbox__icon) {
  color: #1e88e5 !important;
  font-weight: 600 !important;
  font-size: 24rpx !important;
}

/* ==================== 模态框内容 ==================== */
.modal-content {
  padding: 32rpx 0;
  max-height: 600rpx;
  overflow-y: auto;
  line-height: 1.6;
}

/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 750rpx) {
  .logo {
    width: 200rpx;
    height: 200rpx;
  }
  
  .main-title {
    font-size: 44rpx;
  }
  
  .sub-title {
    font-size: 28rpx;
  }
}

@media screen and (max-width: 600rpx) {
  .content {
    padding: 0 24rpx;
  }
  
  .logo {
    width: 180rpx;
    height: 180rpx;
  }
  
  .main-title {
    font-size: 40rpx;
  }
  
  .sub-title {
    font-size: 26rpx;
  }
}
</style>

<!-- 新增：全局page高度修正，确保小程序下全屏 -->
<style>
page {
  height: 100%;
}
</style>
