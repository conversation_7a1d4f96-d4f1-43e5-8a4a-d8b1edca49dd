"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_user = require("../../src/api/modules/user.js");
if (!Array) {
  const _component_up_icon = common_vendor.resolveComponent("up-icon");
  const _component_up_button = common_vendor.resolveComponent("up-button");
  const _component_up_checkbox = common_vendor.resolveComponent("up-checkbox");
  const _component_up_text = common_vendor.resolveComponent("up-text");
  const _component_up_modal = common_vendor.resolveComponent("up-modal");
  const _component_up_toast = common_vendor.resolveComponent("up-toast");
  (_component_up_icon + _component_up_button + _component_up_checkbox + _component_up_text + _component_up_modal + _component_up_toast)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const { setProfile } = userStore;
    const agreedToTerms = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const showUserAgreementModal = common_vendor.ref(false);
    const showPrivacyPolicyModal = common_vendor.ref(false);
    const toastRef = common_vendor.ref(null);
    const modalTextSize = common_vendor.computed(() => 14);
    const modalTextLineHeight = common_vendor.computed(() => 22);
    const loginButtonStyle = common_vendor.computed(() => ({
      width: "80%",
      height: "92rpx",
      backgroundColor: agreedToTerms.value ? "#ffffff" : "rgba(255,255,255,0.6)",
      color: "#1e88e5",
      border: agreedToTerms.value ? "2rpx solid #1e88e5" : "2rpx solid rgba(30,136,229,0.3)",
      borderRadius: "46rpx",
      boxShadow: agreedToTerms.value ? "0 8rpx 24rpx rgba(30,136,229,0.15), 0 2rpx 8rpx rgba(30,136,229,0.1)" : "0 4rpx 12rpx rgba(0,0,0,0.1)",
      transition: "all 0.3s ease",
      opacity: agreedToTerms.value ? 1 : 0.7
    }));
    const userAgreementContent = common_vendor.computed(() => `1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。`);
    const privacyPolicyContent = common_vendor.computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。`);
    function showUserAgreement() {
      showUserAgreementModal.value = true;
    }
    function showPrivacyPolicy() {
      showPrivacyPolicyModal.value = true;
    }
    function showToast(title, type = "info") {
      if (toastRef.value) {
        toastRef.value.show({
          title,
          type,
          duration: type === "success" ? 1500 : 2e3
        });
      }
    }
    async function handleWxLogin() {
      if (!agreedToTerms.value) {
        showToast("请先同意用户协议", "warning");
        return;
      }
      isLoading.value = true;
      try {
        const loginResult = await new Promise((resolve, reject) => {
          common_vendor.index.login({
            provider: "weixin",
            success: resolve,
            fail: reject
          });
        });
        const loginParams = {
          code: loginResult.code
        };
        const userInfo = await src_api_modules_user.wxLogin(loginParams);
        setProfile(userInfo);
        showToast("登录成功", "success");
        setTimeout(() => {
          navigateByUserStatus(userInfo.status);
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:251", "微信登录失败:", error);
        showToast("登录失败，请重试", "error");
      } finally {
        isLoading.value = false;
      }
    }
    function navigateByUserStatus(status) {
      switch (status) {
        case "approved":
          common_vendor.index.reLaunch({ url: "/pages/info/info" });
          break;
        case "pending":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "rejected":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "incomplete":
        default:
          common_vendor.index.navigateTo({ url: "/pages/register/register" });
          break;
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "account",
          size: "80",
          color: "#1e88e5"
        }),
        b: common_vendor.p({
          name: "weixin-fill",
          size: "18",
          color: "#1e88e5"
        }),
        c: common_vendor.o(handleWxLogin),
        d: common_vendor.p({
          type: "primary",
          disabled: !agreedToTerms.value || isLoading.value,
          loading: isLoading.value,
          loadingText: "登录中...",
          customStyle: loginButtonStyle.value,
          shape: "round",
          size: "large",
          throttleTime: 1e3
        }),
        e: common_vendor.o(($event) => agreedToTerms.value = $event),
        f: common_vendor.p({
          activeColor: "#ffffff",
          inactiveColor: "rgba(255,255,255,0.6)",
          iconColor: "#1e88e5",
          size: "20",
          iconSize: "14",
          shape: "square",
          disabled: isLoading.value,
          modelValue: agreedToTerms.value
        }),
        g: common_vendor.o(showUserAgreement),
        h: common_vendor.o(showPrivacyPolicy),
        i: common_vendor.p({
          text: userAgreementContent.value,
          size: modalTextSize.value,
          color: "#212121",
          lineHeight: modalTextLineHeight.value
        }),
        j: common_vendor.o(($event) => showUserAgreementModal.value = false),
        k: common_vendor.o(($event) => showUserAgreementModal.value = $event),
        l: common_vendor.p({
          title: "用户服务协议",
          showCancelButton: false,
          confirmText: "我知道了",
          modelValue: showUserAgreementModal.value
        }),
        m: common_vendor.p({
          text: privacyPolicyContent.value,
          size: modalTextSize.value,
          color: "#212121",
          lineHeight: modalTextLineHeight.value
        }),
        n: common_vendor.o(($event) => showPrivacyPolicyModal.value = false),
        o: common_vendor.o(($event) => showPrivacyPolicyModal.value = $event),
        p: common_vendor.p({
          title: "隐私政策",
          showCancelButton: false,
          confirmText: "我知道了",
          modelValue: showPrivacyPolicyModal.value
        }),
        q: common_vendor.sr(toastRef, "e4e4508d-8", {
          "k": "toastRef"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
