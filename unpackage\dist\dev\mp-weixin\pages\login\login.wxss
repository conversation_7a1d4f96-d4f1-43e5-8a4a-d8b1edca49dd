

/* ==================== 页面全局设置 ==================== */
.login-container.data-v-e4e4508d {
  /* Apple风格渐变+噪点，合并为多层background */
  background:
    linear-gradient(180deg,#60a5fa 0%,#1e88e5 100%),
    radial-gradient(circle at 20% 80%,rgba(255,255,255,.06) 0%,transparent 40%),
    radial-gradient(circle at 80% 20%,rgba(255,255,255,.06) 0%,transparent 40%);
  min-height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-left: 0;
  padding-right: 0;
  overflow: hidden;
}

/* ==================== 主容器 ==================== */
.content.data-v-e4e4508d {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  justify-content: center;
  gap: 160rpx;
  max-width: 600rpx;
  position: relative;
  z-index: 10;
}

/* ==================== Logo区域 ==================== */
.logo.data-v-e4e4508d {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50rpx;
  flex-shrink: 0;
}
.logo-container.data-v-e4e4508d {
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 
    0 8rpx 32rpx rgba(255, 255, 255, 0.1),
    0 2rpx 8rpx rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* ==================== 标题区域 ==================== */
.title-area.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 移除margin-bottom: 200rpx; */
  flex-shrink: 0;
}
.main-title.data-v-e4e4508d {
  font-size: 48rpx;
  color: #ffffff !important;
  font-weight: 600;
  margin-bottom: 20rpx;
  letter-spacing: .5rpx;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}
.sub-title.data-v-e4e4508d {
  font-size: 32rpx;
  color: rgba(255,255,255,.85) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* ==================== 登录按钮区域 ==================== */
.login-btn-container.data-v-e4e4508d {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 0;
  flex-shrink: 0;
}
.btn-content.data-v-e4e4508d {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-text.data-v-e4e4508d {
  color: #1e88e5 !important;
  font-size: 32rpx;
  font-weight: 500;
  margin-left: 12rpx;
}

/* ==================== 协议区域 ==================== */
.agreement.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 24rpx;
}
.agreement-label.data-v-e4e4508d {
  font-size: 26rpx;
  color: #ffffff !important;
  margin-left: 16rpx;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}
.agreement-links.data-v-e4e4508d {
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.agreement-text.data-v-e4e4508d {
  font-size: 24rpx;
  color: rgba(255,255,255,.9) !important;
  margin: 0 8rpx;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}
.link.data-v-e4e4508d {
  font-size: 24rpx;
  color: #ffffff !important;
  text-decoration: underline;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* ==================== uview-plus组件样式强制覆盖 ==================== */
/* 强制设置所有图标颜色 */
.data-v-e4e4508d .up-icon,.data-v-e4e4508d .u-icon {
  color: #ffffff !important;
}

/* 强制设置按钮内文字颜色 */
.data-v-e4e4508d .up-button,.data-v-e4e4508d .u-button {
  color: #ffffff !important;
}
.data-v-e4e4508d .up-button .up-button__text,.data-v-e4e4508d .u-button .u-button__text {
  color: #ffffff !important;
}

/* 强制设置复选框文字颜色 - 确保可见 */
.data-v-e4e4508d .up-checkbox__label,.data-v-e4e4508d .u-checkbox__label {
  color: #ffffff !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

/* ==================== 复选框样式优化 ==================== */
.data-v-e4e4508d .up-checkbox-group,.data-v-e4e4508d .u-checkbox-group {
  display: flex;
  align-items: center;
  justify-content: center;
}
.data-v-e4e4508d .up-checkbox,.data-v-e4e4508d .u-checkbox {
  display: flex;
  align-items: center;
}
.data-v-e4e4508d .up-checkbox__icon-wrap,.data-v-e4e4508d .u-checkbox__icon-wrap {
  margin-right: 0;
  border: 2rpx solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 6rpx !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1) !important;
  width: 40rpx !important;
  height: 40rpx !important;
}

/* 复选框选中状态 */
.data-v-e4e4508d .up-checkbox--checked .up-checkbox__icon-wrap,.data-v-e4e4508d .u-checkbox--checked .u-checkbox__icon-wrap {
  background-color: #ffffff !important;
  border-color: #ffffff !important;
}

/* 复选框图标 */
.data-v-e4e4508d .up-checkbox__icon,.data-v-e4e4508d .u-checkbox__icon {
  color: #1e88e5 !important;
  font-weight: 600 !important;
  font-size: 24rpx !important;
}

/* ==================== 模态框内容 ==================== */
.modal-content.data-v-e4e4508d {
  padding: 32rpx 0;
  max-height: 600rpx;
  overflow-y: auto;
  line-height: 1.6;
}

/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 750rpx) {
.logo.data-v-e4e4508d {
    width: 200rpx;
    height: 200rpx;
}
.main-title.data-v-e4e4508d {
    font-size: 44rpx;
}
.sub-title.data-v-e4e4508d {
    font-size: 28rpx;
}
}
@media screen and (max-width: 600rpx) {
.content.data-v-e4e4508d {
    padding: 0 24rpx;
}
.logo.data-v-e4e4508d {
    width: 180rpx;
    height: 180rpx;
}
.main-title.data-v-e4e4508d {
    font-size: 40rpx;
}
.sub-title.data-v-e4e4508d {
    font-size: 26rpx;
}
}


page {
  height: 100%;
}
