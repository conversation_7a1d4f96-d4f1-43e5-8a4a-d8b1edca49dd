<view class="login-container data-v-e4e4508d"><view class="content data-v-e4e4508d"><view class="logo data-v-e4e4508d"><view class="logo-container data-v-e4e4508d"><up-icon wx:if="{{a}}" class="data-v-e4e4508d" u-i="e4e4508d-0" bind:__l="__l" u-p="{{a}}"/></view></view><view class="title-area data-v-e4e4508d"><text class="main-title data-v-e4e4508d">疾控考试系统</text><text class="sub-title data-v-e4e4508d">医护任职资格考试平台</text></view><view class="login-btn-container data-v-e4e4508d"><up-button wx:if="{{d}}" class="data-v-e4e4508d" u-s="{{['d']}}" bindclick="{{c}}" u-i="e4e4508d-1" bind:__l="__l" u-p="{{d}}"><view class="btn-content data-v-e4e4508d"><up-icon wx:if="{{b}}" class="data-v-e4e4508d" u-i="e4e4508d-2,e4e4508d-1" bind:__l="__l" u-p="{{b}}"/><text class="btn-text data-v-e4e4508d">微信授权登录</text></view></up-button></view><view class="agreement data-v-e4e4508d"><view class="checkbox-wrapper data-v-e4e4508d"><up-checkbox wx:if="{{f}}" class="data-v-e4e4508d" u-s="{{['d']}}" u-i="e4e4508d-3" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"> 我已阅读并同意 </up-checkbox></view><view class="agreement-links data-v-e4e4508d"><text class="link data-v-e4e4508d" bindtap="{{g}}">《用户服务协议》</text><text class="agreement-text data-v-e4e4508d">和</text><text class="link data-v-e4e4508d" bindtap="{{h}}">《隐私政策》</text></view></view></view><up-modal wx:if="{{l}}" class="data-v-e4e4508d" u-s="{{['d']}}" bindconfirm="{{j}}" u-i="e4e4508d-4" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"><view class="modal-content data-v-e4e4508d"><up-text wx:if="{{i}}" class="data-v-e4e4508d" u-i="e4e4508d-5,e4e4508d-4" bind:__l="__l" u-p="{{i}}"/></view></up-modal><up-modal wx:if="{{p}}" class="data-v-e4e4508d" u-s="{{['d']}}" bindconfirm="{{n}}" u-i="e4e4508d-6" bind:__l="__l" bindupdateModelValue="{{o}}" u-p="{{p}}"><view class="modal-content data-v-e4e4508d"><up-text wx:if="{{m}}" class="data-v-e4e4508d" u-i="e4e4508d-7,e4e4508d-6" bind:__l="__l" u-p="{{m}}"/></view></up-modal><up-toast class="r data-v-e4e4508d" u-r="toastRef" u-i="e4e4508d-8" bind:__l="__l"/></view>